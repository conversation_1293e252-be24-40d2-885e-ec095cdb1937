﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="ImGUI">
      <UniqueIdentifier>{e6ee5e8d-4d4a-400b-a0df-8cf173e60aa6}</UniqueIdentifier>
    </Filter>
    <Filter Include="GUI">
      <UniqueIdentifier>{96e51ace-2e46-4c80-8c70-a3f77137becd}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\ImGUI\imconfig.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\ImGUI\imgui.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\ImGUI\imgui_internal.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\ImGUI\imstb_rectpack.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\ImGUI\imstb_textedit.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\ImGUI\imstb_truetype.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\Backend\imgui_impl_win32.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="GUI\font.h">
      <Filter>GUI</Filter>
    </ClInclude>
    <ClInclude Include="GUI\gui.h">
      <Filter>GUI</Filter>
    </ClInclude>
    <ClInclude Include="GUI\colorpicker.h">
      <Filter>GUI</Filter>
    </ClInclude>
    <ClInclude Include="GUI\imgui_tricks.hpp">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\Backend\imgui_impl_dx11.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
    <ClInclude Include="..\ImGUI\imgui_freetype.h">
      <Filter>ImGUI</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\ImGUI\imgui.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
    <ClCompile Include="..\ImGUI\imgui_draw.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
    <ClCompile Include="..\ImGUI\imgui_widgets.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
    <ClCompile Include="..\Backend\imgui_impl_win32.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp">
      <Filter>GUI</Filter>
    </ClCompile>
    <ClCompile Include="GUI\gui.cpp">
      <Filter>GUI</Filter>
    </ClCompile>
    <ClCompile Include="GUI\colorpicker.cpp">
      <Filter>GUI</Filter>
    </ClCompile>
    <ClCompile Include="GUI\imgui_tricks.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
    <ClCompile Include="..\Backend\imgui_impl_dx11.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
    <ClCompile Include="..\ImGUI\imgui_freetype.cpp">
      <Filter>ImGUI</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="imgui.ini" />
    <None Include="x64\Release\DirectX 11.exe.recipe" />
    <None Include="x64\Release\DirectX 11.iobj" />
    <None Include="x64\Release\DirectX 11.ipdb" />
    <None Include="x64\Release\vc143.pdb" />
    <None Include="x64\Release\DirectX 11.tlog\CL.command.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\Cl.items.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\CL.read.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\CL.write.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\DirectX 11.lastbuildstate" />
    <None Include="x64\Release\DirectX 11.tlog\link.command.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\link.read.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\link.write.1.tlog" />
    <None Include="x64\Debug\vc143.idb" />
    <None Include="x64\Debug\vc143.pdb" />
    <None Include="x64\Debug\DirectX 11.tlog\CL.command.1.tlog" />
    <None Include="x64\Debug\DirectX 11.tlog\CL.read.1.tlog" />
    <None Include="x64\Debug\DirectX 11.tlog\CL.write.1.tlog" />
    <None Include="x64\Debug\DirectX 11.tlog\DirectX 11.lastbuildstate" />
    <None Include="x64\Debug\DirectX 11.tlog\unsuccessfulbuild" />
  </ItemGroup>
  <ItemGroup>
    <Font Include="GUI\resources\font.ttf" />
    <Font Include="GUI\resources\fontb.ttf" />
    <Font Include="GUI\resources\Glyphter.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Object Include="x64\Release\colorpicker.obj" />
    <Object Include="x64\Release\gui.obj" />
    <Object Include="x64\Release\imgui.obj" />
    <Object Include="x64\Release\imgui_draw.obj" />
    <Object Include="x64\Release\imgui_freetype.obj" />
    <Object Include="x64\Release\imgui_impl_dx11.obj" />
    <Object Include="x64\Release\imgui_impl_win32.obj" />
    <Object Include="x64\Release\imgui_tricks.obj" />
    <Object Include="x64\Release\imgui_widgets.obj" />
    <Object Include="x64\Release\main.obj" />
    <Object Include="x64\Debug\colorpicker.obj" />
    <Object Include="x64\Debug\gui.obj" />
    <Object Include="x64\Debug\imgui.obj" />
    <Object Include="x64\Debug\imgui_draw.obj" />
    <Object Include="x64\Debug\imgui_impl_dx11.obj" />
    <Object Include="x64\Debug\imgui_impl_win32.obj" />
    <Object Include="x64\Debug\imgui_tricks.obj" />
    <Object Include="x64\Debug\imgui_widgets.obj" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="x64\Release\DirectX 9.log" />
    <Text Include="x64\Debug\DirectX 9.log" />
  </ItemGroup>
</Project>