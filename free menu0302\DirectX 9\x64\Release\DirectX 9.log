﻿  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui.cpp
  imgui_draw.cpp
  imgui_freetype.cpp
  imgui_widgets.cpp
  colorpicker.cpp
  gui.cpp
  imgui_tricks.cpp
  main.cpp
  Previous IPDB not found, fall back to full compilation.
MSVCRT.lib(initializers.obj) : warning LNK4098: библиотека по умолчанию "libcmt.lib" конфликтует с использованием других библиотек; используйте /NODEFAULTLIB:library
  Создание кода
  All 2305 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Создание кода завершено
  DirectX 9.vcxproj -> D:\0aimgui\projects\free menu0302\x64\Release\DirectX 11.exe
