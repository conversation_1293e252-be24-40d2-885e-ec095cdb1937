<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{68843c8f-4705-4c58-8266-4a7653c0d6ef}</ProjectGuid>
    <RootNamespace>DirectX9</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>DirectX 11</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IncludePath>$(SolutionDir)/include;$(ProjectDir)/GUI;$(DXSDK_DIR)/include;$(SolutionDir)/ImGui;$(SolutionDir)/Backend</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IncludePath>$(SolutionDir)/include;$(ProjectDir)/GUI;$(DXSDK_DIR)/include;$(SolutionDir)/ImGui;$(SolutionDir)/Backend</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>$(SolutionDir)/include;$(ProjectDir)/GUI;$(DXSDK_DIR)/include;$(SolutionDir)/ImGui;$(SolutionDir)/Backend</IncludePath>
    <LibraryPath>C:\Program Files %28x86%29\Microsoft DirectX SDK %28June 2010%29\Lib\x64;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>$(SolutionDir)/include;$(ProjectDir)/GUI;$(DXSDK_DIR)include;$(SolutionDir)/ImGui;$(SolutionDir)/Backend;$(SolutionDir)thirdparty\include</IncludePath>
    <LibraryPath>C:\Program Files %28x86%29\Microsoft DirectX SDK %28June 2010%29\Lib\x64;$(SolutionDir)thirdparty\lib;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d9.lib;d3dx9.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d9.lib;d3dx9.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d11.lib;d3dx11.lib;d3dcompiler.lib;dxgi.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>d3d11.lib;d3dx11.lib;d3dcompiler.lib;dxgi.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\Backend\imgui_impl_dx11.h" />
    <ClInclude Include="..\Backend\imgui_impl_win32.h" />
    <ClInclude Include="..\ImGUI\imconfig.h" />
    <ClInclude Include="..\ImGUI\imgui.h" />
    <ClInclude Include="..\ImGUI\imgui_freetype.h" />
    <ClInclude Include="..\ImGUI\imgui_internal.h" />
    <ClInclude Include="..\ImGUI\imstb_rectpack.h" />
    <ClInclude Include="..\ImGUI\imstb_textedit.h" />
    <ClInclude Include="..\ImGUI\imstb_truetype.h" />
    <ClInclude Include="GUI\colorpicker.h" />
    <ClInclude Include="GUI\font.h" />
    <ClInclude Include="GUI\gui.h" />
    <ClInclude Include="GUI\imgui_tricks.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Backend\imgui_impl_dx11.cpp" />
    <ClCompile Include="..\Backend\imgui_impl_win32.cpp" />
    <ClCompile Include="..\ImGUI\imgui.cpp" />
    <ClCompile Include="..\ImGUI\imgui_draw.cpp" />
    <ClCompile Include="..\ImGUI\imgui_freetype.cpp" />
    <ClCompile Include="..\ImGUI\imgui_widgets.cpp" />
    <ClCompile Include="GUI\colorpicker.cpp" />
    <ClCompile Include="GUI\gui.cpp" />
    <ClCompile Include="GUI\imgui_tricks.cpp" />
    <ClCompile Include="main.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="imgui.ini" />
    <None Include="x64\Debug\DirectX 11.tlog\CL.command.1.tlog" />
    <None Include="x64\Debug\DirectX 11.tlog\CL.read.1.tlog" />
    <None Include="x64\Debug\DirectX 11.tlog\CL.write.1.tlog" />
    <None Include="x64\Debug\DirectX 11.tlog\DirectX 11.lastbuildstate" />
    <None Include="x64\Debug\DirectX 11.tlog\unsuccessfulbuild" />
    <None Include="x64\Debug\vc143.idb" />
    <None Include="x64\Debug\vc143.pdb" />
    <None Include="x64\Release\DirectX 11.exe.recipe" />
    <None Include="x64\Release\DirectX 11.iobj" />
    <None Include="x64\Release\DirectX 11.ipdb" />
    <None Include="x64\Release\DirectX 11.tlog\CL.command.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\Cl.items.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\CL.read.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\CL.write.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\DirectX 11.lastbuildstate" />
    <None Include="x64\Release\DirectX 11.tlog\link.command.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\link.read.1.tlog" />
    <None Include="x64\Release\DirectX 11.tlog\link.write.1.tlog" />
    <None Include="x64\Release\vc143.pdb" />
  </ItemGroup>
  <ItemGroup>
    <Font Include="GUI\resources\font.ttf" />
    <Font Include="GUI\resources\fontb.ttf" />
    <Font Include="GUI\resources\Glyphter.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Object Include="x64\Debug\colorpicker.obj" />
    <Object Include="x64\Debug\gui.obj" />
    <Object Include="x64\Debug\imgui.obj" />
    <Object Include="x64\Debug\imgui_draw.obj" />
    <Object Include="x64\Debug\imgui_impl_dx11.obj" />
    <Object Include="x64\Debug\imgui_impl_win32.obj" />
    <Object Include="x64\Debug\imgui_tricks.obj" />
    <Object Include="x64\Debug\imgui_widgets.obj" />
    <Object Include="x64\Release\colorpicker.obj" />
    <Object Include="x64\Release\gui.obj" />
    <Object Include="x64\Release\imgui.obj" />
    <Object Include="x64\Release\imgui_draw.obj" />
    <Object Include="x64\Release\imgui_freetype.obj" />
    <Object Include="x64\Release\imgui_impl_dx11.obj" />
    <Object Include="x64\Release\imgui_impl_win32.obj" />
    <Object Include="x64\Release\imgui_tricks.obj" />
    <Object Include="x64\Release\imgui_widgets.obj" />
    <Object Include="x64\Release\main.obj" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="x64\Debug\DirectX 9.log" />
    <Text Include="x64\Release\DirectX 9.log" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>